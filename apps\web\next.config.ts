import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Fix React 19 + Next.js 15 compatibility issues
  experimental: {
    reactCompiler: false,
    forceSwcTransforms: true,
    // Disable styled-jsx for React 19 compatibility
    swcPlugins: [],
  },
  // Disable standalone build to prevent Windows symlink issues
  // Move serverComponentsExternalPackages to the correct location
  serverExternalPackages: ['styled-jsx', 'firebase', '@firebase/util', '@firebase/auth'],
  // Completely disable styled-jsx to prevent React 19 compatibility issues
  compiler: {
    styledComponents: false,
    styledJsx: false,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'images.example.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'res.cloudinary.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
  // Environment-based test route control
  async rewrites() {
    const isDevelopment = process.env.NODE_ENV === 'development';
    const enableTestRoutes = process.env.ENABLE_TEST_ROUTES === 'true';

    if (isDevelopment && enableTestRoutes) {
      return [
        // Redirect old test routes to new organized structure
        {
          source: '/test-auth',
          destination: '/tests/pages/auth/test-auth',
        },
        {
          source: '/test-all-notifications',
          destination: '/tests/pages/notifications/test-all-notifications',
        },
        {
          source: '/test-customer',
          destination: '/tests/pages/business/test-customer',
        },
        {
          source: '/test-notifications',
          destination: '/tests/pages/notifications/test-notifications',
        },
        {
          source: '/test-restaurant',
          destination: '/tests/pages/business/test-restaurant',
        },
        {
          source: '/test-vendor',
          destination: '/tests/pages/business/test-vendor',
        },
        {
          source: '/test-webhook',
          destination: '/tests/pages/integrations/test-webhook',
        },
        {
          source: '/test-complete-flow',
          destination: '/tests/pages/business/test-complete-flow',
        },
        {
          source: '/test-admin',
          destination: '/tests/pages/utilities/test-admin',
        },
        {
          source: '/test-simple',
          destination: '/tests/pages/utilities/test-simple',
        },
        {
          source: '/test-chat',
          destination: '/tests/pages/integrations/test-chat',
        },
        {
          source: '/test-auth-flow',
          destination: '/tests/pages/auth/test-auth-flow',
        },
        {
          source: '/test-customers',
          destination: '/tests/pages/business/test-customers',
        },
      ];
    }

    return [];
  },
  async redirects() {
    const isDevelopment = process.env.NODE_ENV === 'development';
    const enableTestRoutes = process.env.ENABLE_TEST_ROUTES === 'true';

    if (!isDevelopment || !enableTestRoutes) {
      // In production or when tests are disabled, redirect test routes to home
      return [
        {
          source: '/tests/:path*',
          destination: '/',
          permanent: false,
        },
        {
          source: '/test-:path*',
          destination: '/',
          permanent: false,
        },
      ];
    }

    return [];
  },
  eslint: {
    // Allow production builds to complete even with ESLint warnings
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Allow production builds to complete even with TypeScript errors
    ignoreBuildErrors: true,
  },
  // Disable static optimization for error pages to prevent styled-jsx issues
  generateBuildId: async () => {
    return 'build-' + Date.now();
  },
  // Use standalone mode (symlink warnings are cosmetic only)
  output: 'standalone',
  // Consolidated webpack configuration
  webpack: (config, { isServer }) => {
    // Suppress specific webpack warnings from Supabase realtime and Firebase
    config.ignoreWarnings = [
      {
        module: /node_modules\/@supabase\/realtime-js/,
        message: /Critical dependency: the request of a dependency is an expression/,
      },
      {
        module: /node_modules\/firebase/,
        message: /Attempted import error/,
      },
      {
        module: /node_modules\/@firebase/,
        message: /is not exported from/,
      },
      // Suppress styled-jsx warnings
      {
        module: /node_modules\/styled-jsx/,
        message: /.*useContext.*/,
      },
      // Suppress React 19 compatibility warnings
      {
        module: /node_modules\/react/,
        message: /.*useContext.*/,
      },
    ];

    // Enhanced module resolution for better TypeScript path mapping support
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': require('path').resolve(__dirname, 'src'),
    };

    // Ensure proper module resolution for TypeScript files
    config.resolve.extensions = ['.tsx', '.ts', '.jsx', '.js', '.mjs', '.json'];

    // Fix monorepo module resolution for Firebase and other packages
    config.resolve.modules = [
      require('path').resolve(__dirname, 'node_modules'),
      require('path').resolve(__dirname, '../../node_modules'),
      'node_modules'
    ];

    // Ensure Firebase modules are resolved from root node_modules
    config.resolve.alias = {
      ...config.resolve.alias,
      'firebase/app': require('path').resolve(__dirname, '../../node_modules/firebase/app'),
      'firebase/auth': require('path').resolve(__dirname, '../../node_modules/firebase/auth'),
      'firebase/firestore': require('path').resolve(__dirname, '../../node_modules/firebase/firestore'),
      'firebase/storage': require('path').resolve(__dirname, '../../node_modules/firebase/storage'),
      'firebase/messaging': require('path').resolve(__dirname, '../../node_modules/firebase/messaging'),
      // Completely replace styled-jsx with our empty module
      'styled-jsx': require('path').resolve(__dirname, 'src/lib/empty-styled-jsx.js'),
      'styled-jsx/style': require('path').resolve(__dirname, 'src/lib/empty-styled-jsx.js'),
      'styled-jsx/css': require('path').resolve(__dirname, 'src/lib/empty-styled-jsx.js'),
    };

    // Add fallbacks for missing Firebase util functions
    config.resolve.fallback = {
      ...config.resolve.fallback,
      'crypto': false,
      'stream': false,
      'util': false,
    };

    // Handle externals for server-side builds only
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push(
        // Exclude Firebase from server-side rendering to prevent initialization issues
        ({ request }: { request?: string }, callback: (error?: Error | null, result?: string) => void) => {
          if (request?.startsWith('firebase/') || request?.startsWith('@firebase/')) {
            return callback(null, `commonjs ${request}`);
          }
          // Don't externalize styled-jsx - let our alias handle it
          callback();
        }
      );
    }

    // Add error handling for build failures
    config.stats = {
      errorDetails: true,
      warnings: false, // Suppress warnings to focus on errors
    };

    // Note: styled-jsx replacement is now handled via resolve.alias above

    return config;
  },
  turbopack: {
    // Turbopack configuration to complement webpack setup
    rules: {
      // Add any custom loader rules here if needed
      // For now, this ensures Turbopack is configured alongside webpack
    },
    resolveExtensions: ['.tsx', '.ts', '.jsx', '.js', '.mjs', '.json'],
  },
};

export default nextConfig;
