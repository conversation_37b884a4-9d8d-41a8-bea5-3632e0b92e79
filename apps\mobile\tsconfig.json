{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": false, "skipLibCheck": true, "noImplicitAny": false, "jsx": "preserve", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "bundler", "noEmit": true, "target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "paths": {"@/*": ["./src/*"]}, "incremental": true, "isolatedModules": true, "plugins": [{"name": "next"}]}, "include": ["**/*.ts", "**/*.tsx", "nativewind-env.d.ts", "src/types/**/*.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules", "scripts", "android", "ios", ".expo"]}