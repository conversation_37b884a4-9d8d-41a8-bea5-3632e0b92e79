'use client';

import React, { createContext, useContext, ReactNode } from 'react';

// Create a minimal style context to replace styled-jsx
interface StyleContextType {
  styles: Record<string, string>;
}

const StyleContext = createContext<StyleContextType>({ styles: {} });

interface StyleProviderProps {
  children: ReactNode;
}

export function StyleProvider({ children }: StyleProviderProps) {
  const contextValue: StyleContextType = {
    styles: {}
  };

  return (
    <StyleContext.Provider value={contextValue}>
      {children}
    </StyleContext.Provider>
  );
}

export function useStyles() {
  return useContext(StyleContext);
}

// Export a mock StyleRegistry component to replace styled-jsx's
export function StyleRegistry({ children }: { children: ReactNode }) {
  return <>{children}</>;
}

export default StyleProvider;
