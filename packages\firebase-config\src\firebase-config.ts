// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";
import { getMessaging, isSupported } from "firebase/messaging";

// Validate environment variables with better error handling
const requiredEnvVars = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Check for missing environment variables
const missingVars = Object.entries(requiredEnvVars)
  .filter(([, value]) => !value)
  .map(([key]) => key);

// Only throw error in development, log warning in production to prevent crashes
if (missingVars.length > 0) {
  const errorMessage = `Missing Firebase environment variables: ${missingVars.join(', ')}`;

  if (process.env.NODE_ENV === 'development') {
    throw new Error(errorMessage);
  } else {
    console.error('Firebase Config Error:', errorMessage);
    console.warn('Firebase services may not work properly without these environment variables');
  }
}

// Your web app's Firebase configuration with fallbacks
const firebaseConfig = {
  apiKey: requiredEnvVars.apiKey || 'dummy-api-key',
  authDomain: requiredEnvVars.authDomain || 'dummy-project.firebaseapp.com',
  projectId: requiredEnvVars.projectId || 'dummy-project',
  storageBucket: requiredEnvVars.storageBucket || 'dummy-project.appspot.com',
  messagingSenderId: requiredEnvVars.messagingSenderId || '123456789',
  appId: requiredEnvVars.appId || '1:123456789:web:dummy'
};

// Initialize Firebase with error handling
let app: any = null;
let auth: any = null;
let db: any = null;
let storage: any = null;

try {
  app = initializeApp(firebaseConfig);

  // Initialize Firebase services with error handling
  auth = getAuth(app);
  db = getFirestore(app);
  storage = getStorage(app);

  console.log('Firebase initialized successfully');
} catch (error) {
  console.error('Firebase initialization error:', error);

  // Create dummy objects to prevent crashes
  auth = null;
  db = null;
  storage = null;
}

// Export Firebase services
export { auth, db, storage };

// Initialize Firebase Cloud Messaging (only in browser environment)
let messaging: unknown = null;

// FCM initialization function with better error handling
export const initializeMessaging = async () => {
  if (typeof window !== 'undefined' && app) {
    try {
      const supported = await isSupported();
      if (supported) {
        messaging = getMessaging(app);
        console.log('Firebase Cloud Messaging initialized successfully');
        return messaging;
      } else {
        console.warn('Firebase Cloud Messaging is not supported in this browser');
        return null;
      }
    } catch (error) {
      console.error('Error initializing Firebase Cloud Messaging:', error);
      return null;
    }
  }
  return null;
};

// Get messaging instance
export const getMessagingInstance = () => messaging;

export { messaging, firebaseConfig };

// Export app as both default and named export for compatibility
export { app };
export default app;
